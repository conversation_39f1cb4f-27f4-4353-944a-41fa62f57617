#!/usr/bin/env python3

import time
from isaacgym import gymapi
from isaacgym import gymutil

def debug_ur5_minimal():
    # Parse arguments
    args = gymutil.parse_arguments(description="UR5 Minimal Debug")
    
    # Initialize gym
    gym = gymapi.acquire_gym()
    
    # Configure sim with minimal settings
    sim_params = gymapi.SimParams()
    sim_params.dt = 1.0 / 60.0
    sim_params.substeps = 2
    sim_params.up_axis = gymapi.UP_AXIS_Z
    sim_params.gravity = gymapi.Vec3(0.0, 0.0, -9.8)
    
    # Create sim
    sim = gym.create_sim(args.compute_device_id, args.graphics_device_id, args.physics_engine, sim_params)
    if sim is None:
        raise Exception("Failed to create sim")

    # Add ground plane
    plane_params = gymapi.PlaneParams()
    plane_params.normal = gymapi.Vec3(0, 0, 1)
    gym.add_ground(sim, plane_params)

    # Create environment
    env_lower = gymapi.Vec3(-5, -5, 0)
    env_upper = gymapi.Vec3(5, 5, 5)
    env = gym.create_env(sim, env_lower, env_upper, 1)

    # Test 1: Create a simple colored box for reference
    print("=== TEST 1: Creating reference box ===")
    box_asset_options = gymapi.AssetOptions()
    box_asset = gym.create_box(sim, 0.5, 0.5, 0.5, box_asset_options)
    box_pose = gymapi.Transform()
    box_pose.p = gymapi.Vec3(-2, 0, 0.25)
    box_pose.r = gymapi.Quat(0, 0, 0, 1)
    box_handle = gym.create_actor(env, box_asset, box_pose, 'reference_box', 1, 1)
    gym.set_rigid_body_color(env, box_handle, 0, gymapi.MESH_VISUAL_AND_COLLISION, gymapi.Vec3(0, 1, 0))  # Green
    print("✅ Reference box created")

    # Test 2: Load UR5 with MINIMAL asset options
    print("\n=== TEST 2: Loading UR5 with minimal options ===")
    asset_root = "../assets"
    ur5_asset_path = "ur5_description/robots/ur5.urdf"
    
    # Absolute minimal asset options - just like Franka
    asset_options = gymapi.AssetOptions()
    asset_options.fix_base_link = True
    asset_options.disable_gravity = True
    asset_options.flip_visual_attachments = True
    asset_options.default_dof_drive_mode = gymapi.DOF_MODE_POS
    
    print(f"Loading UR5 from: {asset_root}/{ur5_asset_path}")
    
    try:
        ur5_asset = gym.load_asset(sim, asset_root, ur5_asset_path, asset_options)
        print("✅ UR5 asset loaded successfully!")
        
        # Get basic info
        rigid_body_names = gym.get_asset_rigid_body_names(ur5_asset)
        dof_count = gym.get_asset_dof_count(ur5_asset)
        
        print(f"UR5 rigid bodies: {rigid_body_names}")
        print(f"UR5 DOF count: {dof_count}")
        
    except Exception as e:
        print(f"❌ Failed to load UR5 asset: {e}")
        return

    # Test 3: Create UR5 actor with minimal pose
    print("\n=== TEST 3: Creating UR5 actor ===")
    ur5_pose = gymapi.Transform()
    ur5_pose.p = gymapi.Vec3(0, 0, 0)  # On ground
    ur5_pose.r = gymapi.Quat(0, 0, 0, 1)  # No rotation
    
    ur5_handle = gym.create_actor(env, ur5_asset, ur5_pose, 'ur5_debug', 1, 1)
    
    if ur5_handle is None:
        print("❌ Failed to create UR5 actor")
        return
    else:
        print(f"✅ UR5 actor created successfully")

    # Test 4: Set UR5 to simple pose
    print("\n=== TEST 4: Setting UR5 pose ===")
    dof_states = gym.get_actor_dof_states(env, ur5_handle, gymapi.STATE_POS)
    print(f"Current DOF state shape: {dof_states['pos'].shape}")
    print(f"Current joint positions: {dof_states['pos']}")
    
    # Set to simple extended pose
    simple_pose = [0.5, -1.0, 1.0, -1.5, 0.0, 0.5]  # Simple readable pose
    dof_states['pos'] = simple_pose
    gym.set_actor_dof_states(env, ur5_handle, dof_states, gymapi.STATE_POS)
    print(f"✅ Set UR5 to simple pose: {simple_pose}")

    # Test 5: Color the UR5 bright red
    print("\n=== TEST 5: Coloring UR5 ===")
    for i in range(len(rigid_body_names)):
        gym.set_rigid_body_color(env, ur5_handle, i, gymapi.MESH_VISUAL_AND_COLLISION, gymapi.Vec3(1, 0, 0))
    print("✅ UR5 colored bright red")

    # Create viewer
    viewer = gym.create_viewer(sim, gymapi.CameraProperties())
    if viewer is None:
        print("❌ Failed to create viewer")
        return

    # Position camera to see both box and UR5
    cam_pos = gymapi.Vec3(3, 3, 2)
    cam_target = gymapi.Vec3(0, 0, 0.5)
    gym.viewer_camera_look_at(viewer, None, cam_pos, cam_target)
    
    print(f"\n🎥 Camera positioned at {cam_pos}, looking at {cam_target}")
    print("\n🔍 You should see:")
    print("  - GREEN reference box at (-2, 0, 0.25)")
    print("  - BRIGHT RED UR5 robot at origin (0, 0, 0)")
    print("\n📊 Debug Results:")
    print(f"  - UR5 rigid bodies: {len(rigid_body_names)}")
    print(f"  - UR5 DOF count: {dof_count}")
    print(f"  - Body names: {rigid_body_names}")
    print("\nIf you can see the GREEN box but NOT the RED UR5:")
    print("  → UR5 mesh loading issue")
    print("If you can see BOTH:")
    print("  → UR5 loading works, issue is in teleop configuration")
    print("If you can see NEITHER:")
    print("  → General rendering issue")
    print("\nPress 'q' to quit")

    # Run simulation
    while not gym.query_viewer_has_closed(viewer):
        gym.simulate(sim)
        gym.fetch_results(sim, True)
        gym.step_graphics(sim)
        gym.draw_viewer(viewer, sim, True)
        gym.sync_frame_time(sim)

    # Cleanup
    gym.destroy_viewer(viewer)
    gym.destroy_sim(sim)

if __name__ == "__main__":
    debug_ur5_minimal() 