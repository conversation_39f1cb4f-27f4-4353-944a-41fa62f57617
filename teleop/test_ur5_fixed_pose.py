#!/usr/bin/env python3

import time
from isaacgym import gymapi
from isaacgym import gymutil
import numpy as np

def test_ur5_fixed_pose():
    # Parse arguments
    args = gymutil.parse_arguments(description="UR5 Fixed Pose Test")
    
    # Initialize gym
    gym = gymapi.acquire_gym()
    
    # Configure sim
    sim_params = gymapi.SimParams()
    sim_params.dt = 1.0 / 60.0
    sim_params.substeps = 2
    sim_params.up_axis = gymapi.UP_AXIS_Z
    sim_params.gravity = gymapi.Vec3(0.0, 0.0, -9.8)
    
    # Create sim
    sim = gym.create_sim(args.compute_device_id, args.graphics_device_id, args.physics_engine, sim_params)
    if sim is None:
        raise Exception("Failed to create sim")

    # Add ground plane
    plane_params = gymapi.PlaneParams()
    plane_params.normal = gymapi.Vec3(0, 0, 1)
    gym.add_ground(sim, plane_params)

    # Create environment
    env_lower = gymapi.Vec3(-5, -5, 0)
    env_upper = gymapi.Vec3(5, 5, 5)
    env = gym.create_env(sim, env_lower, env_upper, 1)

    # Load UR5 with SIMPLIFIED options (exactly like working Franka)
    asset_root = "../assets"
    ur5_asset_path = "ur5_description/robots/ur5.urdf"
    
    asset_options = gymapi.AssetOptions()
    asset_options.fix_base_link = True
    asset_options.disable_gravity = True
    asset_options.flip_visual_attachments = True
    asset_options.default_dof_drive_mode = gymapi.DOF_MODE_POS
    asset_options.use_mesh_materials = True
    asset_options.mesh_normal_mode = gymapi.COMPUTE_PER_VERTEX
    
    # Use EXACT same simple settings as Franka (remove complex settings)
    asset_options.vhacd_enabled = False
    asset_options.replace_cylinder_with_capsule = True
    asset_options.density = 1000.0
    
    print(f"Loading UR5 from: {asset_root}/{ur5_asset_path}")
    
    try:
        ur5_asset = gym.load_asset(sim, asset_root, ur5_asset_path, asset_options)
        print("✅ UR5 asset loaded successfully!")
        
        # Get asset info
        body_names = gym.get_asset_rigid_body_names(ur5_asset)
        dof_count = gym.get_asset_dof_count(ur5_asset)
        
        print(f"UR5 rigid bodies: {body_names}")
        print(f"UR5 DOF count: {dof_count}")
        
    except Exception as e:
        print(f"❌ Failed to load UR5 asset: {e}")
        return

    # Create UR5 at a clearly visible position
    ur5_pose = gymapi.Transform()
    ur5_pose.p = gymapi.Vec3(0, 0, 0)  # On ground level
    ur5_pose.r = gymapi.Quat(0, 0, 0, 1)  # No rotation
    
    ur5_handle = gym.create_actor(env, ur5_asset, ur5_pose, 'ur5_test', 1, 1)
    
    if ur5_handle is None:
        print("❌ Failed to create UR5 actor")
        return
    else:
        print(f"✅ UR5 actor created at position: {ur5_pose.p}")

    # Set UR5 to a CLEAR, EXTENDED pose (not collapsed)
    dof_states = gym.get_actor_dof_states(env, ur5_handle, gymapi.STATE_POS)
    
    # Use a clear "extended arm" pose - these are good joint angles for UR5 visibility
    extended_pose = [
        0.0,      # shoulder_pan_joint: straight forward
        -1.57,    # shoulder_lift_joint: arm up 90 degrees  
        1.57,     # elbow_joint: elbow bent 90 degrees
        -1.57,    # wrist_1_joint: wrist straight
        0.0,      # wrist_2_joint: no twist
        0.0       # wrist_3_joint: no end effector rotation
    ]
    
    dof_states['pos'] = extended_pose
    gym.set_actor_dof_states(env, ur5_handle, dof_states, gymapi.STATE_POS)
    
    print(f"✅ UR5 joints set to EXTENDED pose: {extended_pose}")
    print("This should show a clearly visible, extended robotic arm")

    # Make UR5 bright red for maximum visibility
    for i in range(len(body_names)):
        gym.set_rigid_body_color(env, ur5_handle, i, gymapi.MESH_VISUAL_AND_COLLISION, gymapi.Vec3(1, 0, 0))
    
    print("✅ UR5 colored bright RED for maximum visibility")

    # Add reference objects for scale
    # Small box for scale reference
    box_asset_options = gymapi.AssetOptions()
    small_box_asset = gym.create_box(sim, 0.1, 0.1, 0.1, box_asset_options)
    
    box_pose = gymapi.Transform()
    box_pose.p = gymapi.Vec3(-0.5, 0, 0.05)  # Small box near UR5 base
    box_pose.r = gymapi.Quat(0, 0, 0, 1)
    
    box_handle = gym.create_actor(env, small_box_asset, box_pose, 'scale_box', 1, 1)
    gym.set_rigid_body_color(env, box_handle, 0, gymapi.MESH_VISUAL_AND_COLLISION, gymapi.Vec3(0, 1, 0))  # Green
    
    print(f"✅ Green scale reference box (10cm) created at {box_pose.p}")

    # Large box for comparison
    large_box_asset = gym.create_box(sim, 0.5, 0.5, 0.5, box_asset_options)
    
    large_box_pose = gymapi.Transform()
    large_box_pose.p = gymapi.Vec3(1.5, 0, 0.25)  # Large box to the side
    large_box_pose.r = gymapi.Quat(0, 0, 0, 1)
    
    large_box_handle = gym.create_actor(env, large_box_asset, large_box_pose, 'large_box', 1, 1)
    gym.set_rigid_body_color(env, large_box_handle, 0, gymapi.MESH_VISUAL_AND_COLLISION, gymapi.Vec3(0, 0, 1))  # Blue
    
    print(f"✅ Blue large box (50cm) created at {large_box_pose.p}")

    # Create viewer
    viewer = gym.create_viewer(sim, gymapi.CameraProperties())
    if viewer is None:
        print("❌ Failed to create viewer")
        return

    # Position camera optimally for UR5 viewing
    cam_pos = gymapi.Vec3(2, 2, 1.5)  # Good angle to see extended arm
    cam_target = gymapi.Vec3(0.3, 0, 0.5)  # Look at middle of extended arm
    gym.viewer_camera_look_at(viewer, None, cam_pos, cam_target)
    
    print(f"\n🎥 Camera positioned at {cam_pos}, looking at {cam_target}")
    print("\n🔍 You should now see:")
    print("  - BRIGHT RED UR5 robot arm in EXTENDED pose at origin")
    print("  - Small GREEN box (10cm) near the UR5 base for scale")
    print("  - Large BLUE box (50cm) to the right for scale comparison")
    print("\nThe UR5 should be clearly visible and properly extended!")
    print("If the UR5 still looks wrong, please describe exactly what you see")
    print("Press 'q' to quit the test")

    # Run simulation with some movement to make it more visible
    frame_count = 0
    while not gym.query_viewer_has_closed(viewer):
        # Simulate
        gym.simulate(sim)
        gym.fetch_results(sim, True)
        
        # Optional: Add slight movement to make UR5 more noticeable
        frame_count += 1
        if frame_count % 120 == 0:  # Every 2 seconds
            # Slightly vary the shoulder pan to make it more visible
            current_states = gym.get_actor_dof_states(env, ur5_handle, gymapi.STATE_POS)
            current_states['pos'][0] = 0.2 * np.sin(frame_count * 0.01)  # Small oscillation
            gym.set_actor_dof_states(env, ur5_handle, current_states, gymapi.STATE_POS)
        
        gym.step_graphics(sim)
        gym.draw_viewer(viewer, sim, True)
        gym.sync_frame_time(sim)

    # Cleanup
    gym.destroy_viewer(viewer)
    gym.destroy_sim(sim)

if __name__ == "__main__":
    test_ur5_fixed_pose() 