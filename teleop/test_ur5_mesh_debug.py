#!/usr/bin/env python3

import time
from isaacgym import gymapi
from isaacgym import gymutil

def test_ur5_mesh_debug():
    # Parse arguments
    args = gymutil.parse_arguments(description="UR5 Mesh Debug Test")
    
    # Initialize gym
    gym = gymapi.acquire_gym()
    
    # Configure sim
    sim_params = gymapi.SimParams()
    sim_params.dt = 1.0 / 60.0
    sim_params.substeps = 2
    sim_params.up_axis = gymapi.UP_AXIS_Z
    sim_params.gravity = gymapi.Vec3(0.0, 0.0, -9.8)
    
    # Create sim
    sim = gym.create_sim(args.compute_device_id, args.graphics_device_id, args.physics_engine, sim_params)
    if sim is None:
        raise Exception("Failed to create sim")

    # Add ground plane
    plane_params = gymapi.PlaneParams()
    plane_params.normal = gymapi.Vec3(0, 0, 1)
    gym.add_ground(sim, plane_params)

    # Create environment
    env_lower = gymapi.Vec3(-5, -5, 0)
    env_upper = gymapi.Vec3(5, 5, 5)
    env = gym.create_env(sim, env_lower, env_upper, 1)

    # Test 1: UR5 with standard mesh loading
    print("=== TEST 1: UR5 with standard mesh loading ===")
    asset_root = "../assets"
    ur5_asset_path = "ur5_description/robots/ur5.urdf"
    
    asset_options1 = gymapi.AssetOptions()
    asset_options1.fix_base_link = True
    asset_options1.disable_gravity = True
    asset_options1.flip_visual_attachments = True
    asset_options1.default_dof_drive_mode = gymapi.DOF_MODE_POS
    asset_options1.use_mesh_materials = True
    asset_options1.mesh_normal_mode = gymapi.COMPUTE_PER_VERTEX
    asset_options1.vhacd_enabled = False
    asset_options1.replace_cylinder_with_capsule = True
    asset_options1.density = 1000.0
    
    try:
        ur5_asset1 = gym.load_asset(sim, asset_root, ur5_asset_path, asset_options1)
        print("✅ UR5 asset loaded with mesh materials")
        
        ur5_pose1 = gymapi.Transform()
        ur5_pose1.p = gymapi.Vec3(-1, 0, 0)
        ur5_pose1.r = gymapi.Quat(0, 0, 0, 1)
        
        ur5_handle1 = gym.create_actor(env, ur5_asset1, ur5_pose1, 'ur5_meshes', 1, 1)
        
        # Set pose and color
        dof_states1 = gym.get_actor_dof_states(env, ur5_handle1, gymapi.STATE_POS)
        dof_states1['pos'] = [0, -1.57, 1.57, -1.57, -1.57, 0]
        gym.set_actor_dof_states(env, ur5_handle1, dof_states1, gymapi.STATE_POS)
        
        # Try to set a bright color
        gym.set_rigid_body_color(env, ur5_handle1, 0, gymapi.MESH_VISUAL_AND_COLLISION, gymapi.Vec3(1, 0, 0))  # Red base
        
        print(f"✅ UR5 with meshes created at {ur5_pose1.p} (should be RED)")
        
    except Exception as e:
        print(f"❌ Failed to load UR5 with meshes: {e}")

    # Test 2: UR5 without mesh materials
    print("\n=== TEST 2: UR5 without mesh materials ===")
    asset_options2 = gymapi.AssetOptions()
    asset_options2.fix_base_link = True
    asset_options2.disable_gravity = True
    asset_options2.flip_visual_attachments = True
    asset_options2.default_dof_drive_mode = gymapi.DOF_MODE_POS
    asset_options2.use_mesh_materials = False  # Disable mesh materials
    asset_options2.vhacd_enabled = False
    asset_options2.replace_cylinder_with_capsule = True
    asset_options2.density = 1000.0
    
    try:
        ur5_asset2 = gym.load_asset(sim, asset_root, ur5_asset_path, asset_options2)
        print("✅ UR5 asset loaded without mesh materials")
        
        ur5_pose2 = gymapi.Transform()
        ur5_pose2.p = gymapi.Vec3(1, 0, 0)
        ur5_pose2.r = gymapi.Quat(0, 0, 0, 1)
        
        ur5_handle2 = gym.create_actor(env, ur5_asset2, ur5_pose2, 'ur5_no_materials', 1, 1)
        
        # Set pose and color
        dof_states2 = gym.get_actor_dof_states(env, ur5_handle2, gymapi.STATE_POS)
        dof_states2['pos'] = [0, -1.57, 1.57, -1.57, -1.57, 0]
        gym.set_actor_dof_states(env, ur5_handle2, dof_states2, gymapi.STATE_POS)
        
        # Try to set a bright color
        gym.set_rigid_body_color(env, ur5_handle2, 0, gymapi.MESH_VISUAL_AND_COLLISION, gymapi.Vec3(0, 1, 0))  # Green base
        
        print(f"✅ UR5 without materials created at {ur5_pose2.p} (should be GREEN)")
        
    except Exception as e:
        print(f"❌ Failed to load UR5 without materials: {e}")

    # Test 3: Add a simple visible box for reference
    print("\n=== TEST 3: Reference box ===")
    box_asset_options = gymapi.AssetOptions()
    box_asset = gym.create_box(sim, 0.2, 0.2, 0.2, box_asset_options)
    
    box_pose = gymapi.Transform()
    box_pose.p = gymapi.Vec3(0, 1, 0.1)
    box_pose.r = gymapi.Quat(0, 0, 0, 1)
    
    box_handle = gym.create_actor(env, box_asset, box_pose, 'reference_box', 1, 1)
    gym.set_rigid_body_color(env, box_handle, 0, gymapi.MESH_VISUAL_AND_COLLISION, gymapi.Vec3(0, 0, 1))  # Blue box
    
    print(f"✅ Blue reference box created at {box_pose.p}")

    # Create viewer
    viewer = gym.create_viewer(sim, gymapi.CameraProperties())
    if viewer is None:
        print("❌ Failed to create viewer")
        return

    # Position camera to see all test objects
    cam_pos = gymapi.Vec3(3, 3, 2)
    cam_target = gymapi.Vec3(0, 0, 0.5)
    gym.viewer_camera_look_at(viewer, None, cam_pos, cam_target)
    
    print(f"\n🎥 Camera positioned at {cam_pos}, looking at {cam_target}")
    print("\n🔍 You should see:")
    print("  - RED UR5 at (-1, 0, 0) - with mesh materials")
    print("  - GREEN UR5 at (1, 0, 0) - without mesh materials") 
    print("  - BLUE box at (0, 1, 0) - reference object")
    print("\nIf you can see the blue box but not the UR5s, it's a mesh rendering issue")
    print("If you can't see anything, it's a broader rendering issue")
    print("Press 'q' to quit the test")

    # Run simulation
    while not gym.query_viewer_has_closed(viewer):
        gym.simulate(sim)
        gym.fetch_results(sim, True)
        gym.step_graphics(sim)
        gym.draw_viewer(viewer, sim, True)
        gym.sync_frame_time(sim)

    # Cleanup
    gym.destroy_viewer(viewer)
    gym.destroy_sim(sim)

if __name__ == "__main__":
    test_ur5_mesh_debug() 