#!/usr/bin/env python3

import time
from isaacgym import gymapi
from isaacgym import gymutil
import numpy as np

def test_ur5_orientations():
    # Parse arguments
    args = gymutil.parse_arguments(description="UR5 Orientation Test")
    
    # Initialize gym
    gym = gymapi.acquire_gym()
    
    # Configure sim
    sim_params = gymapi.SimParams()
    sim_params.dt = 1.0 / 60.0
    sim_params.substeps = 2
    sim_params.up_axis = gymapi.UP_AXIS_Z
    sim_params.gravity = gymapi.Vec3(0.0, 0.0, -9.8)
    
    # Create sim
    sim = gym.create_sim(args.compute_device_id, args.graphics_device_id, args.physics_engine, sim_params)
    if sim is None:
        raise Exception("Failed to create sim")

    # Add ground plane
    plane_params = gymapi.PlaneParams()
    plane_params.normal = gymapi.Vec3(0, 0, 1)
    gym.add_ground(sim, plane_params)

    # Create environment
    env_lower = gymapi.Vec3(-10, -10, 0)
    env_upper = gymapi.Vec3(10, 10, 5)
    env = gym.create_env(sim, env_lower, env_upper, 1)

    # Load UR5 asset
    asset_root = "../assets"
    ur5_asset_path = "ur5_description/robots/ur5.urdf"
    
    asset_options = gymapi.AssetOptions()
    asset_options.fix_base_link = True
    asset_options.disable_gravity = True
    asset_options.flip_visual_attachments = True
    asset_options.default_dof_drive_mode = gymapi.DOF_MODE_POS
    
    print(f"Loading UR5 from: {asset_root}/{ur5_asset_path}")
    ur5_asset = gym.load_asset(sim, asset_root, ur5_asset_path, asset_options)
    
    # Test different base orientations and joint poses
    test_configs = [
        {
            "name": "ORIGINAL_ZERO",
            "base_pos": gymapi.Vec3(-4, 0, 0),
            "base_rot": gymapi.Quat(0, 0, 0, 1),  # No rotation
            "joints": [0, 0, 0, 0, 0, 0],
            "color": gymapi.Vec3(1, 0, 0)  # Red
        },
        {
            "name": "ORIGINAL_EXTENDED", 
            "base_pos": gymapi.Vec3(-2, 0, 0),
            "base_rot": gymapi.Quat(0, 0, 0, 1),  # No rotation
            "joints": [0, -1.57, 1.57, -1.57, 0, 0],
            "color": gymapi.Vec3(0, 1, 0)  # Green
        },
        {
            "name": "ROTATED_180_Z",
            "base_pos": gymapi.Vec3(0, 0, 0),
            "base_rot": gymapi.Quat(0, 0, 1, 0),  # 180° around Z
            "joints": [0, -1.57, 1.57, -1.57, 0, 0],
            "color": gymapi.Vec3(0, 0, 1)  # Blue
        },
        {
            "name": "ROTATED_90_Z",
            "base_pos": gymapi.Vec3(2, 0, 0),
            "base_rot": gymapi.Quat(0, 0, 0.707, 0.707),  # 90° around Z
            "joints": [0, -1.57, 1.57, -1.57, 0, 0],
            "color": gymapi.Vec3(1, 1, 0)  # Yellow
        },
        {
            "name": "FRANKA_LIKE_POSE",
            "base_pos": gymapi.Vec3(4, 0, 0),
            "base_rot": gymapi.Quat(0, 0, 0, 1),  # No rotation
            "joints": [0, -0.785, 0, -2.356, 0, 1.571],  # Franka ready pose adapted
            "color": gymapi.Vec3(1, 0, 1)  # Magenta
        },
        {
            "name": "HOME_POSITION",
            "base_pos": gymapi.Vec3(6, 0, 0),
            "base_rot": gymapi.Quat(0, 0, 0, 1),  # No rotation
            "joints": [0, -1.5708, 0, -1.5708, 0, 0],  # Standard UR5 home
            "color": gymapi.Vec3(0, 1, 1)  # Cyan
        }
    ]
    
    ur5_handles = []
    
    print(f"\nCreating {len(test_configs)} UR5 test configurations:")
    
    for i, config in enumerate(test_configs):
        # Create UR5 at different positions and orientations
        ur5_pose = gymapi.Transform()
        ur5_pose.p = config["base_pos"]
        ur5_pose.r = config["base_rot"]
        
        ur5_handle = gym.create_actor(env, ur5_asset, ur5_pose, f'ur5_{config["name"].lower()}', 1, 1)
        ur5_handles.append(ur5_handle)
        
        # Set joint angles
        dof_states = gym.get_actor_dof_states(env, ur5_handle, gymapi.STATE_POS)
        dof_states['pos'] = config["joints"]
        gym.set_actor_dof_states(env, ur5_handle, dof_states, gymapi.STATE_POS)
        
        # Color each UR5 differently
        rigid_body_names = gym.get_asset_rigid_body_names(ur5_asset)
        for j in range(len(rigid_body_names)):
            gym.set_rigid_body_color(env, ur5_handle, j, gymapi.MESH_VISUAL_AND_COLLISION, config["color"])
        
        print(f"  {config['name']} (x={config['base_pos'].x:.1f}): joints={config['joints']}")

    # Create viewer
    viewer = gym.create_viewer(sim, gymapi.CameraProperties())
    if viewer is None:
        print("❌ Failed to create viewer")
        return

    # Position camera to see all UR5s
    cam_pos = gymapi.Vec3(1, -8, 3)  # Back and up to see all robots
    cam_target = gymapi.Vec3(1, 0, 1)  # Look at center
    gym.viewer_camera_look_at(viewer, None, cam_pos, cam_target)
    
    print(f"\n🎥 Camera positioned to view all 6 UR5 configurations")
    print(f"\n🔍 You should see 6 different UR5 robots from LEFT to RIGHT:")
    print(f"  🔴 RED: Original zero pose")
    print(f"  🟢 GREEN: Original extended pose")  
    print(f"  🔵 BLUE: Rotated 180° base + extended")
    print(f"  🟡 YELLOW: Rotated 90° base + extended")
    print(f"  🟣 MAGENTA: Franka-like pose")
    print(f"  🩵 CYAN: Standard UR5 home position")
    print(f"\n❓ Please identify which configuration(s) look CORRECT:")
    print(f"   - Which robot(s) have proper link orientations?")
    print(f"   - Which robot(s) look like a natural robotic arm?")
    print(f"   - Note any specific issues (upside down, twisted, etc.)")
    print(f"\nThis will help identify the correct base orientation and joint configuration!")
    print(f"\nPress 'q' to quit")

    # Run simulation
    while not gym.query_viewer_has_closed(viewer):
        gym.simulate(sim)
        gym.fetch_results(sim, True)
        gym.step_graphics(sim)
        gym.draw_viewer(viewer, sim, True)
        gym.sync_frame_time(sim)

    # Cleanup
    gym.destroy_viewer(viewer)
    gym.destroy_sim(sim)

if __name__ == "__main__":
    test_ur5_orientations() 