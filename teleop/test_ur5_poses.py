#!/usr/bin/env python3

import time
from isaacgym import gymapi
from isaacgym import gymutil
import numpy as np

def test_ur5_poses():
    # Parse arguments
    args = gymutil.parse_arguments(description="UR5 Multiple Poses Test")
    
    # Initialize gym
    gym = gymapi.acquire_gym()
    
    # Configure sim
    sim_params = gymapi.SimParams()
    sim_params.dt = 1.0 / 60.0
    sim_params.substeps = 2
    sim_params.up_axis = gymapi.UP_AXIS_Z
    sim_params.gravity = gymapi.Vec3(0.0, 0.0, -9.8)
    
    # Create sim
    sim = gym.create_sim(args.compute_device_id, args.graphics_device_id, args.physics_engine, sim_params)
    if sim is None:
        raise Exception("Failed to create sim")

    # Add ground plane
    plane_params = gymapi.PlaneParams()
    plane_params.normal = gymapi.Vec3(0, 0, 1)
    gym.add_ground(sim, plane_params)

    # Create environment
    env_lower = gymapi.Vec3(-10, -10, 0)
    env_upper = gymapi.Vec3(10, 10, 5)
    env = gym.create_env(sim, env_lower, env_upper, 1)

    # Load UR5 asset
    asset_root = "../assets"
    ur5_asset_path = "ur5_description/robots/ur5.urdf"
    
    asset_options = gymapi.AssetOptions()
    asset_options.fix_base_link = True
    asset_options.disable_gravity = True
    asset_options.flip_visual_attachments = True
    asset_options.default_dof_drive_mode = gymapi.DOF_MODE_POS
    asset_options.use_mesh_materials = True
    asset_options.mesh_normal_mode = gymapi.COMPUTE_PER_VERTEX
    asset_options.vhacd_enabled = False
    asset_options.replace_cylinder_with_capsule = True
    asset_options.density = 1000.0
    
    print(f"Loading UR5 from: {asset_root}/{ur5_asset_path}")
    ur5_asset = gym.load_asset(sim, asset_root, ur5_asset_path, asset_options)
    
    # Get asset info
    body_names = gym.get_asset_rigid_body_names(ur5_asset)
    dof_props = gym.get_asset_dof_properties(ur5_asset)
    
    print(f"UR5 joint names and limits:")
    for i, prop in enumerate(dof_props):
        print(f"  Joint {i}: {prop['name']} - Range: [{prop['lower']:.3f}, {prop['upper']:.3f}]")

    # Define different poses to test
    test_poses = {
        "ZERO": [0, 0, 0, 0, 0, 0],
        "HOME": [0, -1.57, 0, -1.57, 0, 0],  # Typical home pose
        "EXTENDED": [0, -1.57, 1.57, -1.57, 0, 0],  # Extended arm
        "REACH_UP": [0, -0.5, 0.5, -2.0, 0, 0],  # Reaching upward
        "REACH_FORWARD": [0, -1.2, 1.2, -1.5, 0, 0],  # Reaching forward
        "FRANKA_LIKE": [0, -0.785, 0, -2.356, 0, 1.571]  # Similar to working Franka pose
    }
    
    ur5_handles = []
    colors = [
        gymapi.Vec3(1, 0, 0),    # Red
        gymapi.Vec3(0, 1, 0),    # Green  
        gymapi.Vec3(0, 0, 1),    # Blue
        gymapi.Vec3(1, 1, 0),    # Yellow
        gymapi.Vec3(1, 0, 1),    # Magenta
        gymapi.Vec3(0, 1, 1)     # Cyan
    ]
    
    positions = [
        gymapi.Vec3(-4, 0, 0),   # Far left
        gymapi.Vec3(-2.4, 0, 0), # Left
        gymapi.Vec3(-0.8, 0, 0), # Center left
        gymapi.Vec3(0.8, 0, 0),  # Center right
        gymapi.Vec3(2.4, 0, 0),  # Right
        gymapi.Vec3(4, 0, 0)     # Far right
    ]
    
    print(f"\nCreating 6 UR5 robots with different poses:")
    
    for i, (pose_name, joint_angles) in enumerate(test_poses.items()):
        # Create UR5 at different positions
        ur5_pose = gymapi.Transform()
        ur5_pose.p = positions[i]
        ur5_pose.r = gymapi.Quat(0, 0, 0, 1)
        
        ur5_handle = gym.create_actor(env, ur5_asset, ur5_pose, f'ur5_{pose_name.lower()}', 1, 1)
        ur5_handles.append(ur5_handle)
        
        # Set joint angles
        dof_states = gym.get_actor_dof_states(env, ur5_handle, gymapi.STATE_POS)
        dof_states['pos'] = joint_angles
        gym.set_actor_dof_states(env, ur5_handle, dof_states, gymapi.STATE_POS)
        
        # Color each UR5 differently
        for j in range(len(body_names)):
            gym.set_rigid_body_color(env, ur5_handle, j, gymapi.MESH_VISUAL_AND_COLLISION, colors[i])
        
        print(f"  {pose_name} (at x={positions[i].x}): {joint_angles} - Color: {['Red', 'Green', 'Blue', 'Yellow', 'Magenta', 'Cyan'][i]}")

    # Add labels above each robot
    for i, pose_name in enumerate(test_poses.keys()):
        label_asset = gym.create_box(sim, 0.1, 0.4, 0.02, gymapi.AssetOptions())
        label_pose = gymapi.Transform()
        label_pose.p = gymapi.Vec3(positions[i].x, 0, 2.0)  # Above the robot
        label_pose.r = gymapi.Quat(0, 0, 0, 1)
        
        label_handle = gym.create_actor(env, label_asset, label_pose, f'label_{pose_name.lower()}', 1, 1)
        # White labels
        gym.set_rigid_body_color(env, label_handle, 0, gymapi.MESH_VISUAL_AND_COLLISION, gymapi.Vec3(1, 1, 1))

    # Create viewer
    viewer = gym.create_viewer(sim, gymapi.CameraProperties())
    if viewer is None:
        print("❌ Failed to create viewer")
        return

    # Position camera to see all robots
    cam_pos = gymapi.Vec3(0, -6, 3)  # Back and up to see all 6 robots
    cam_target = gymapi.Vec3(0, 0, 1)  # Look at center
    gym.viewer_camera_look_at(viewer, None, cam_pos, cam_target)
    
    print(f"\n🎥 Camera positioned to view all 6 UR5 robots")
    print(f"\n🔍 You should see 6 different colored UR5 robots:")
    print(f"  RED (ZERO) - GREEN (HOME) - BLUE (EXTENDED) - YELLOW (REACH_UP) - MAGENTA (REACH_FORWARD) - CYAN (FRANKA_LIKE)")
    print(f"\n❓ Please identify which poses look NORMAL vs WEIRD:")
    print(f"   - Which robot(s) look like a proper robotic arm?")
    print(f"   - Which robot(s) look collapsed, twisted, or unnatural?")
    print(f"   - This will help identify the correct joint configuration!")
    print(f"\nPress 'q' to quit the test")

    # Run simulation
    while not gym.query_viewer_has_closed(viewer):
        gym.simulate(sim)
        gym.fetch_results(sim, True)
        gym.step_graphics(sim)
        gym.draw_viewer(viewer, sim, True)
        gym.sync_frame_time(sim)

    # Cleanup
    gym.destroy_viewer(viewer)
    gym.destroy_sim(sim)

if __name__ == "__main__":
    test_ur5_poses() 