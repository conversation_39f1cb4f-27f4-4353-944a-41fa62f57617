#!/usr/bin/env python3

import time
from isaacgym import gymapi
from isaacgym import gymutil

def test_ur5_visibility():
    # Parse arguments
    args = gymutil.parse_arguments(description="UR5 Visibility Test")
    
    # Initialize gym
    gym = gymapi.acquire_gym()
    
    # Configure sim
    sim_params = gymapi.SimParams()
    sim_params.dt = 1.0 / 60.0
    sim_params.substeps = 2
    sim_params.up_axis = gymapi.UP_AXIS_Z
    sim_params.gravity = gymapi.Vec3(0.0, 0.0, -9.8)
    
    # Create sim
    sim = gym.create_sim(args.compute_device_id, args.graphics_device_id, args.physics_engine, sim_params)
    if sim is None:
        raise Exception("Failed to create sim")

    # Add ground plane
    plane_params = gymapi.PlaneParams()
    plane_params.normal = gymapi.Vec3(0, 0, 1)
    gym.add_ground(sim, plane_params)

    # Create environment
    env_lower = gymapi.Vec3(-5, -5, 0)
    env_upper = gymapi.Vec3(5, 5, 5)
    env = gym.create_env(sim, env_lower, env_upper, 1)

    # Load UR5 with simple asset options (like Franka)
    asset_root = "../assets"
    ur5_asset_path = "ur5_description/robots/ur5.urdf"
    
    asset_options = gymapi.AssetOptions()
    asset_options.fix_base_link = True
    asset_options.disable_gravity = True
    asset_options.flip_visual_attachments = True  # Same as Franka
    asset_options.default_dof_drive_mode = gymapi.DOF_MODE_POS
    asset_options.use_mesh_materials = True
    asset_options.mesh_normal_mode = gymapi.COMPUTE_PER_VERTEX
    
    # Use EXACT same settings as working Franka version
    asset_options.vhacd_enabled = False
    asset_options.replace_cylinder_with_capsule = True
    asset_options.density = 1000.0
    
    print(f"Loading UR5 from: {asset_root}/{ur5_asset_path}")
    
    try:
        ur5_asset = gym.load_asset(sim, asset_root, ur5_asset_path, asset_options)
        print("✅ UR5 asset loaded successfully!")
        
        # Get asset info
        body_names = gym.get_asset_rigid_body_names(ur5_asset)
        dof_count = gym.get_asset_dof_count(ur5_asset)
        
        print(f"UR5 rigid bodies: {body_names}")
        print(f"UR5 DOF count: {dof_count}")
        
    except Exception as e:
        print(f"❌ Failed to load UR5 asset: {e}")
        return

    # Create UR5 actor at origin (same collision groups as Franka: 1, 1)
    ur5_pose = gymapi.Transform()
    ur5_pose.p = gymapi.Vec3(0, 0, 0)  # At origin for easy viewing
    ur5_pose.r = gymapi.Quat(0, 0, 0, 1)
    
    ur5_handle = gym.create_actor(env, ur5_asset, ur5_pose, 'ur5_test', 1, 1)  # Same as Franka
    
    if ur5_handle is None:
        print("❌ Failed to create UR5 actor")
        return
    else:
        print(f"✅ UR5 actor created at position: {ur5_pose.p}")

    # Set UR5 to readable pose (not collapsed)
    dof_states = gym.get_actor_dof_states(env, ur5_handle, gymapi.STATE_POS)
    dof_states['pos'] = [0, -1.57, 1.57, -1.57, -1.57, 0]  # Simple readable pose
    gym.set_actor_dof_states(env, ur5_handle, dof_states, gymapi.STATE_POS)
    
    print(f"✅ UR5 joints set to readable pose: {dof_states['pos']}")

    # Create viewer
    viewer = gym.create_viewer(sim, gymapi.CameraProperties())
    if viewer is None:
        print("❌ Failed to create viewer")
        return

    # Position camera to see UR5
    cam_pos = gymapi.Vec3(2, 2, 1)  # Close enough to see UR5 clearly
    cam_target = gymapi.Vec3(0, 0, 0.5)  # Look at UR5
    gym.viewer_camera_look_at(viewer, None, cam_pos, cam_target)
    
    print(f"🎥 Camera positioned at {cam_pos}, looking at {cam_target}")
    print("\n🔍 Looking for UR5 in Isaac Gym viewer...")
    print("The UR5 should be visible as a blue/grey robotic arm")
    print("Press 'q' to quit the test")

    # Run simulation
    while not gym.query_viewer_has_closed(viewer):
        gym.simulate(sim)
        gym.fetch_results(sim, True)
        gym.step_graphics(sim)
        gym.draw_viewer(viewer, sim, True)
        gym.sync_frame_time(sim)

    # Cleanup
    gym.destroy_viewer(viewer)
    gym.destroy_sim(sim)

if __name__ == "__main__":
    test_ur5_visibility() 