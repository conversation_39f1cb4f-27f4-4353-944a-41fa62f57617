# Visualisation

mesh_files:
  base:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur10e/visual/base.dae
    collision:
      mesh:
        package: ur_description
        path: meshes/ur10e/collision/base.stl
    mesh_offset:
      x: 0.0
      y: 0.0
      z: 0.0
      roll: 0.0
      pitch: 0.0
      yaw: !degrees 180

  shoulder:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur10e/visual/shoulder.dae
    collision:
      mesh:
        package: ur_description
        path: meshes/ur10e/collision/shoulder.stl
    mesh_offset:
      x: 0.0
      y: 0.0
      z: 0.0
      roll: 0.0
      pitch: 0.0
      yaw: !degrees 180

  upper_arm:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur10e/visual/upperarm.dae
    collision:
      mesh:
        package: ur_description
        path: meshes/ur10e/collision/upperarm.stl
      mesh_files:
    mesh_offset:
      x: 0.0
      y: 0.0
      z: 0.1762
      roll: !degrees 90
      pitch: 0.0
      yaw: !degrees -90

  forearm:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur10e/visual/forearm.dae
    collision:
      mesh:
        package: ur_description
        path: meshes/ur10e/collision/forearm.stl
    mesh_offset:
      x: 0.0
      y: 0.0
      z: 0.0393
      roll: !degrees 90
      pitch: 0.0
      yaw: !degrees -90

  wrist_1:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur10e/visual/wrist1.dae
    collision:
      mesh:
        package: ur_description
        path: meshes/ur10e/collision/wrist1.stl
    mesh_offset:
      x: 0.0
      y: 0.0
      z: -0.135
      roll: !degrees 90
      pitch: 0.0
      yaw: 0.0

  wrist_2:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur10e/visual/wrist2.dae
    collision:
      mesh:
        package: ur_description
        path: meshes/ur10e/collision/wrist2.stl
    mesh_offset:
      x: 0.0
      y: 0.0
      z: -0.12
      roll: 0.0
      pitch: 0.0
      yaw: 0.0

  wrist_3:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur10e/visual/wrist3.dae
    collision:
      mesh:
        package: ur_description
        path: meshes/ur10e/collision/wrist3.stl
    mesh_offset:
      x: 0.0
      y: 0.0
      z: -0.1168
      roll: !degrees 90
      pitch: 0.0
      yaw: 0.0
