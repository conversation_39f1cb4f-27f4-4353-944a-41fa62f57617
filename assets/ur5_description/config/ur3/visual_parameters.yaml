# Visualisation

mesh_files:
  base:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur3/visual/base.dae
    collision:
      mesh:
        package: ur_description
        path: meshes/ur3/collision/base.stl
    mesh_offset:
      x: 0.0
      y: 0.0
      z: 0.0
      roll: 0.0
      pitch: 0.0
      yaw: !degrees 180

  shoulder:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur3/visual/shoulder.dae
    collision:
      mesh:
        package: ur_description
        path: meshes/ur3/collision/shoulder.stl
    mesh_offset:
      x: 0.0
      y: 0.0
      z: 0.0
      roll: 0.0
      pitch: 0.0
      yaw: !degrees 180

  upper_arm:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur3/visual/upperarm.dae
    collision:
      mesh:
        package: ur_description
        path: meshes/ur3/collision/upperarm.stl
      mesh_files:
    mesh_offset:
      x: 0.0
      y: 0.0
      z: 0.1198
      roll: !degrees 90
      pitch: 0.0
      yaw: !degrees -90

  forearm:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur3/visual/forearm.dae
    collision:
      mesh:
        package: ur_description
        path: meshes/ur3/collision/forearm.stl
    mesh_offset:
      x: 0.0
      y: 0.0
      z: 0.0275
      roll: !degrees 90
      pitch: 0.0
      yaw: !degrees -90

  wrist_1:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur3/visual/wrist1.dae
    collision:
      mesh:
        package: ur_description
        path: meshes/ur3/collision/wrist1.stl
    mesh_offset:
      x: 0.0
      y: 0.0
      z: -0.085
      roll: !degrees 90
      pitch: 0.0
      yaw: 0.0

  wrist_2:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur3/visual/wrist2.dae
    collision:
      mesh:
        package: ur_description
        path: meshes/ur3/collision/wrist2.stl
    mesh_offset:
      x: 0.0
      y: 0.0
      z: -0.083
      roll: 0.0
      pitch: 0.0
      yaw: 0.0

  wrist_3:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur3/visual/wrist3.dae
    collision:
      mesh:
        package: ur_description
        path: meshes/ur3/collision/wrist3.stl
    mesh_offset:
      x: 0.0
      y: -0.00255
      z: -0.082
      roll: !degrees 90
      pitch: 0.0
      yaw: 0.0
