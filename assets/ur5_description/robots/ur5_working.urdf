<?xml version="1.0"?>
<robot name="ur5_rg2">

  <!-- Arm Links -->
  <link name="base_link">
    <visual name="base_link_visual">
      <geometry>
        <mesh filename="package://ur5_rg2_ign/meshes/visual/ur5/base.dae"/>
      </geometry>
    </visual>
    <collision name="base_link_collision">
      <geometry>
        <mesh filename="package://ur5_rg2_ign/meshes/collision/ur5/base.stl"/>
      </geometry>
    </collision>
  </link>

  <link name="shoulder_link">
    <visual name="shoulder_link_visual">
      <geometry>
        <mesh filename="package://ur5_rg2_ign/meshes/visual/ur5/shoulder.dae"/>
      </geometry>
    </visual>
    <collision name="shoulder_link_collision">
      <geometry>
        <mesh filename="package://ur5_rg2_ign/meshes/collision/ur5/shoulder.stl"/>
      </geometry>
    </collision>
  </link>

  <link name="upper_arm_link">
    <visual name="upper_arm_link_visual">
      <geometry>
        <mesh filename="package://ur5_rg2_ign/meshes/visual/ur5/upperarm.dae"/>
      </geometry>
    </visual>
    <collision name="upper_arm_link_collision">
      <geometry>
        <mesh filename="package://ur5_rg2_ign/meshes/collision/ur5/upperarm.stl"/>
      </geometry>
    </collision>
  </link>

  <link name="forearm_link">
    <visual name="forearm_link_visual">
      <geometry>
        <mesh filename="package://ur5_rg2_ign/meshes/visual/ur5/forearm.dae"/>
      </geometry>
    </visual>
    <collision name="forearm_link_collision">
      <geometry>
        <mesh filename="package://ur5_rg2_ign/meshes/collision/ur5/forearm.stl"/>
      </geometry>
    </collision>
  </link>

  <link name="wrist_1_link">
    <visual name="wrist_1_link_visual">
      <geometry>
        <mesh filename="package://ur5_rg2_ign/meshes/visual/ur5/wrist1.dae"/>
      </geometry>
    </visual>
    <collision name="wrist_1_link_collision">
      <geometry>
        <mesh filename="package://ur5_rg2_ign/meshes/collision/ur5/wrist1.stl"/>
      </geometry>
    </collision>
  </link>

  <link name="wrist_2_link">
    <visual name="wrist_2_link_visual">
      <geometry>
        <mesh filename="package://ur5_rg2_ign/meshes/visual/ur5/wrist2.dae"/>
      </geometry>
    </visual>
    <collision name="wrist_2_link_collision">
      <geometry>
        <mesh filename="package://ur5_rg2_ign/meshes/collision/ur5/wrist2.stl"/>
      </geometry>
    </collision>
  </link>

  <link name="wrist_3_link">
    <visual name="wrist_3_link_visual">
      <geometry>
        <mesh filename="package://ur5_rg2_ign/meshes/visual/ur5/wrist3.dae"/>
      </geometry>
    </visual>
    <collision name="wrist_3_link_collision">
      <geometry>
        <mesh filename="package://ur5_rg2_ign/meshes/collision/ur5/wrist3.stl"/>
      </geometry>
    </collision>
  </link>


  <!-- Arm Joints -->
  <joint name="shoulder_pan_joint" type="revolute">
    <parent link="base_link"/>
    <child link="shoulder_link"/>
    <origin xyz="0 0 0.089159" rpy="0 0 0"/>
    <axis xyz="0 0 1"/>
    <limit lower="-6.28319" upper="6.28319" effort="150.0" velocity="3.15"/>
  </joint>

  <joint name="shoulder_lift_joint" type="revolute">
    <parent link="shoulder_link"/>
    <child link="upper_arm_link"/>
    <origin xyz="0 0.13585 0" rpy="0 0 0"/>
    <axis xyz="0 1 0"/>
    <limit lower="-6.28319" upper="6.28319" effort="150.0" velocity="3.15"/>
  </joint>

  <joint name="elbow_joint" type="revolute">
    <parent link="upper_arm_link"/>
    <child link="forearm_link"/>
    <origin xyz="0 -0.1197 0.425" rpy="0 0 0"/>
    <axis xyz="0 1 0"/>
    <limit lower="-6.28319" upper="6.28319" effort="150.0" velocity="3.15"/>
  </joint>

  <joint name="wrist_1_joint" type="revolute">
    <parent link="forearm_link"/>
    <child link="wrist_1_link"/>
    <origin xyz="0 0 0.39225" rpy="0 0 0"/>
    <axis xyz="0 1 0"/>
    <limit lower="-6.28319" upper="6.28319" effort="28.0" velocity="3.2"/>
  </joint>

  <joint name="wrist_2_joint" type="revolute">
    <parent link="wrist_1_link"/>
    <child link="wrist_2_link"/>
    <origin xyz="0 0.093 0" rpy="0 0 0"/>
    <axis xyz="0 0 1"/>
    <limit lower="-6.28319" upper="6.28319" effort="28.0" velocity="3.2"/>
  </joint>

  <joint name="wrist_3_joint" type="revolute">
    <parent link="wrist_2_link"/>
    <child link="wrist_3_link"/>
    <origin xyz="0 0 0.09465" rpy="0 0 0"/>
    <axis xyz="0 1 0"/>
    <limit lower="-6.28319" upper="6.28319" effort="28.0" velocity="3.2"/>
  </joint>


  <!-- Arm-Hand Fixed Joint -->
  <joint name="ur5_hand_joint" type="fixed">
    <parent link="wrist_3_link"/>
    <child link="rg2_hand"/>
    <origin xyz="0 0.0823 0" rpy="1.570796325 0 1.570796325"/>
    <limit lower="0" upper="0" effort="0" velocity="0"/>
  </joint>


  <!-- Hand Links -->
  <link name="rg2_hand">
    <visual name="rg2_hand_visual">
      <geometry>
        <mesh filename="package://ur5_rg2_ign/meshes/visual/rg2/hand.dae"/>
      </geometry>
    </visual>
    <collision name="rg2_hand_collision">
      <geometry>
        <mesh filename="package://ur5_rg2_ign/meshes/collision/rg2/hand.stl"/>
      </geometry>
    </collision>
  </link>

  <link name="rg2_leftfinger">
    <visual>
      <geometry>
        <mesh filename="package://ur5_rg2_ign/meshes/visual/rg2/finger.dae" />
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://ur5_rg2_ign/meshes/collision/rg2/finger.stl" />
      </geometry>
    </collision>
  </link>

  <link name="rg2_rightfinger">
    <visual>
      <origin rpy="3.141592653589793 0 0" xyz="0 0 0" />
      <geometry>
        <mesh filename="package://ur5_rg2_ign/meshes/visual/rg2/finger.dae" />
      </geometry>
    </visual>
    <collision>
      <origin rpy="3.141592653589793 0 0" xyz="0 0 0" />
      <geometry>
        <mesh filename="package://ur5_rg2_ign/meshes/collision/rg2/finger.stl" />
      </geometry>
    </collision>
  </link>


  <!-- Finger Joints -->
  <joint name="rg2_finger_joint1" type="revolute">
    <parent link="rg2_hand"/>
    <child link="rg2_leftfinger"/>
    <origin rpy="0 0 0" xyz="0.105 0.017 0"/>
    <axis xyz="0 0 1" />
    <limit effort="10.6" lower="0.0" upper="1.18" velocity="1.57" />
  </joint>

  <joint name="rg2_finger_joint2" type="revolute">
    <parent link="rg2_hand" />
    <child link="rg2_rightfinger" />
    <origin rpy="0 0 0" xyz="0.105 -0.017 0"/>
    <axis xyz="0 0 -1" />
    <limit effort="10.6" lower="0.0" upper="1.18" velocity="1.57" />
    <mimic joint="panda_finger_joint1" />
  </joint>


  <!-- End-effector -->
  <link name="tool0"/>
  <joint name="end_effector_frame_fixed_joint" type="fixed">
    <parent link="wrist_3_link"/>
    <child link="tool0"/>
    <origin xyz="0 0.275 0" rpy="-1.570796325 0 0"/>
  </joint>
</robot>
