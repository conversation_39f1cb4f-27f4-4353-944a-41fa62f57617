<?xml version="1.0"?>
<robot xmlns:xacro="http://wiki.ros.org/xacro" name="ur5_robot">
  <!--
    This is a convenience top-level xacro which loads the macro for the UR5e
    which defines the default values for the various "parameters files"
    parameters for a UR5e.

    This file is only useful when loading a stand-alone, completely isolated
    robot with only default values for all parameters such as the kinematics,
    visual and physical parameters and joint limits.

    This file is not intended to be integrated into a larger scene or other
    composite xacro.

    Instead, xacro:include 'inc/ur5e_macro.xacro' and override the defaults
    for the arguments to that macro.

    Refer to 'inc/ur_macro.xacro' for more information.
  -->
  <xacro:include filename="$(find ur_description)/urdf/inc/ur5_macro.xacro"/>
  <xacro:ur5_robot prefix="" />
</robot>
