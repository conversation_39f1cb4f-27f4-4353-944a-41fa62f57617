<?xml version="1.0"?>
<robot name="ur5">

  <!-- Base Link -->
  <link name="base_link">
    <visual name="base_link_visual">
      <geometry>
        <mesh filename="ur5_description/meshes/visual/base.dae"/>
      </geometry>
    </visual>
    <collision name="base_link_collision">
      <geometry>
        <mesh filename="ur5_description/meshes/collision/base.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="4.0"/>
      <inertia ixx="0.00443333156" ixy="0.0" ixz="0.0" iyy="0.00443333156" iyz="0.0" izz="0.0072"/>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </inertial>
  </link>

  <link name="shoulder_link">
    <visual name="shoulder_link_visual">
      <origin xyz="0 0 0" rpy="0 0 3.14159"/>
      <geometry>
        <mesh filename="ur5_description/meshes/visual/shoulder.dae"/>
      </geometry>
    </visual>
    <collision name="shoulder_link_collision">
      <origin xyz="0 0 0" rpy="0 0 3.14159"/>
      <geometry>
        <mesh filename="ur5_description/meshes/collision/shoulder.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="3.7"/>
      <inertia ixx="0.010267495893" ixy="0.0" ixz="0.0" iyy="0.010267495893" iyz="0.0" izz="0.00666"/>
      <origin xyz="0.0 0.02561 0.0" rpy="0.0 0.0 0.0"/>
    </inertial>
  </link>

  <link name="upper_arm_link">
    <visual name="upper_arm_link_visual">
      <origin xyz="0 0 0.13585" rpy="1.5708 0 -1.5708"/>
      <geometry>
        <mesh filename="ur5_description/meshes/visual/upperarm.dae"/>
      </geometry>
    </visual>
    <collision name="upper_arm_link_collision">
      <origin xyz="0 0 0.13585" rpy="1.5708 0 -1.5708"/>
      <geometry>
        <mesh filename="ur5_description/meshes/collision/upperarm.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="8.393"/>
      <inertia ixx="0.22689067591" ixy="0.0" ixz="0.0" iyy="0.22689067591" iyz="0.0" izz="0.0151074"/>
      <origin xyz="0.0 -0.024201 0.2125" rpy="0.0 0.0 0.0"/>
    </inertial>
  </link>

  <link name="forearm_link">
    <visual name="forearm_link_visual">
      <origin xyz="0 0 0.01615" rpy="1.5708 0 -1.5708"/>
      <geometry>
        <mesh filename="ur5_description/meshes/visual/forearm.dae"/>
      </geometry>
    </visual>
    <collision name="forearm_link_collision">
      <origin xyz="0 0 0.01615" rpy="1.5708 0 -1.5708"/>
      <geometry>
        <mesh filename="ur5_description/meshes/collision/forearm.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="2.275"/>
      <inertia ixx="0.049443313556" ixy="0.0" ixz="0.0" iyy="0.049443313556" iyz="0.0" izz="0.004095"/>
      <origin xyz="0.0 0.0 0.196125" rpy="0.0 0.0 0.0"/>
    </inertial>
  </link>

  <link name="wrist_1_link">
    <visual name="wrist_1_link_visual">
      <origin xyz="0 0 -0.093" rpy="1.5708 0 0"/>
      <geometry>
        <mesh filename="ur5_description/meshes/visual/wrist1.dae"/>
      </geometry>
    </visual>
    <collision name="wrist_1_link_collision">
      <origin xyz="0 0 -0.093" rpy="1.5708 0 0"/>
      <geometry>
        <mesh filename="ur5_description/meshes/collision/wrist1.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="1.219"/>
      <inertia ixx="0.111172755531" ixy="0.0" ixz="0.0" iyy="0.111172755531" iyz="0.0" izz="0.21942"/>
      <origin xyz="0.0 0.093 0.0" rpy="0.0 0.0 0.0"/>
    </inertial>
  </link>

  <link name="wrist_2_link">
    <visual name="wrist_2_link_visual">
      <origin xyz="0 0 -0.09465" rpy="0 0 0"/>
      <geometry>
        <mesh filename="ur5_description/meshes/visual/wrist2.dae"/>
      </geometry>
    </visual>
    <collision name="wrist_2_link_collision">
      <origin xyz="0 0 -0.09465" rpy="0 0 0"/>
      <geometry>
        <mesh filename="ur5_description/meshes/collision/wrist2.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="1.219"/>
      <inertia ixx="0.111172755531" ixy="0.0" ixz="0.0" iyy="0.111172755531" iyz="0.0" izz="0.21942"/>
      <origin xyz="0.0 0.0 0.09465" rpy="0.0 0.0 0.0"/>
    </inertial>
  </link>

  <link name="wrist_3_link">
    <visual name="wrist_3_link_visual">
      <origin xyz="0 0 -0.0823" rpy="1.5708 0 0"/>
      <geometry>
        <mesh filename="ur5_description/meshes/visual/wrist3.dae"/>
      </geometry>
    </visual>
    <collision name="wrist_3_link_collision">
      <origin xyz="0 0 -0.0823" rpy="1.5708 0 0"/>
      <geometry>
        <mesh filename="ur5_description/meshes/collision/wrist3.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1879"/>
      <inertia ixx="0.0171364731454" ixy="0.0" ixz="0.0" iyy="0.0171364731454" iyz="0.0" izz="0.033822"/>
      <origin xyz="0.0 0.0823 0.0" rpy="0.0 0.0 0.0"/>
    </inertial>
  </link>

  <!-- Tool Flange Link (End-effector mounting point) -->
  <link name="tool_flange">
    <inertial>
      <mass value="0.001"/>
      <inertia ixx="0.000001" ixy="0.0" ixz="0.0" iyy="0.000001" iyz="0.0" izz="0.000001"/>
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0"/>
    </inertial>
  </link>

  <!-- Joint definitions -->
  <joint name="shoulder_pan_joint" type="revolute">
    <parent link="base_link"/>
    <child link="shoulder_link"/>
    <origin xyz="0 0 0.089159" rpy="0 0 0"/>
    <axis xyz="0 0 1"/>
    <limit lower="-6.28319" upper="6.28319" effort="150.0" velocity="3.15"/>
    <dynamics damping="10.0"/>
  </joint>

  <joint name="shoulder_lift_joint" type="revolute">
    <parent link="shoulder_link"/>
    <child link="upper_arm_link"/>
    <origin xyz="0 0.13585 0" rpy="0 0 0"/>
    <axis xyz="0 1 0"/>
    <limit lower="-6.28319" upper="6.28319" effort="150.0" velocity="3.15"/>
    <dynamics damping="10.0"/>
  </joint>

  <joint name="elbow_joint" type="revolute">
    <parent link="upper_arm_link"/>
    <child link="forearm_link"/>
    <origin xyz="0 -0.1197 0.425" rpy="0 0 0"/>
    <axis xyz="0 1 0"/>
    <limit lower="-6.28319" upper="6.28319" effort="150.0" velocity="3.15"/>
    <dynamics damping="10.0"/>
  </joint>

  <joint name="wrist_1_joint" type="revolute">
    <parent link="forearm_link"/>
    <child link="wrist_1_link"/>
    <origin xyz="0 0 0.39225" rpy="0 0 0"/>
    <axis xyz="0 1 0"/>
    <limit lower="-6.28319" upper="6.28319" effort="28.0" velocity="3.2"/>
    <dynamics damping="5.0"/>
  </joint>

  <joint name="wrist_2_joint" type="revolute">
    <parent link="wrist_1_link"/>
    <child link="wrist_2_link"/>
    <origin xyz="0 0.093 0" rpy="0 0 0"/>
    <axis xyz="0 0 1"/>
    <limit lower="-6.28319" upper="6.28319" effort="28.0" velocity="3.2"/>
    <dynamics damping="5.0"/>
  </joint>

  <joint name="wrist_3_joint" type="revolute">
    <parent link="wrist_2_link"/>
    <child link="wrist_3_link"/>
    <origin xyz="0 0 0.09465" rpy="0 0 0"/>
    <axis xyz="0 1 0"/>
    <limit lower="-6.28319" upper="6.28319" effort="28.0" velocity="3.2"/>
    <dynamics damping="5.0"/>
  </joint>

  <!-- Tool flange joint - where inspire hands will be mounted -->
  <joint name="tool_flange_joint" type="fixed">
    <parent link="wrist_3_link"/>
    <child link="tool_flange"/>
    <origin xyz="0 0.0823 0" rpy="1.5708 0 1.5708"/>
  </joint>

</robot>
